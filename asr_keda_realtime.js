#!/usr/bin/env node
/**
 * 讯飞 AST 实时转写 Node.js Demo（优化版）
 * - PCM 16k/16bit/单声道，每 40ms 发送 1280 字节
 * - 数字签名：按参数名升序 -> URL 编码 -> 拼接 -> HMAC-SHA1 -> Base64
 * - 建联 -> 收结果 -> 发送结束标记 {"end":true,"sessionId": "..."}
 * - 解析 data.cn.st.rt.ws.cw.w 为识别文本
 * - 心跳 ping，发送节流，错误兜底
 *
 * 依赖：ws
 *   npm i ws
 */

const fs = require("fs");
const path = require("path");
const crypto = require("crypto");
const WebSocket = require("ws");

// ======== 全局常量 ========
const AUDIO_FRAME_SIZE = 1280;   // 16k/16bit 单声道，40ms = 1280字节
const FRAME_INTERVAL_MS = 40;
const DEFAULT_WS_BASE = "wss://office-api-ast-dx.iflyaisol.com/ast/communicate/v1";

// 默认固定参数（与文档一致）
const DEFAULT_FIXED_PARAMS = {
  audio_encode: "pcm_s16le",
  lang: "autodialect",
  samplerate: "16000", // 仅 pcm 必传
};

// 简易参数解析（避免引入额外库）
function parseArgs(argv) {
  const args = {};
  for (const item of argv.slice(2)) {
    const m = item.match(/^--([^=]+)=(.*)$/);
    if (m) args[m[1]] = m[2];
  }
  return args;
}

// UTC 字符串（文档要求形如 2025-09-04T15:38:07+0800）
function buildUtcString(now = new Date()) {
  const pad2 = (n) => String(n).padStart(2, "0");
  const Y = now.getFullYear();
  const M = pad2(now.getMonth() + 1);
  const D = pad2(now.getDate());
  const h = pad2(now.getHours());
  const m = pad2(now.getMinutes());
  const s = pad2(now.getSeconds());
  const tzMin = -now.getTimezoneOffset(); // 东八区 -> +480
  const sign = tzMin >= 0 ? "+" : "-";
  const absMin = Math.abs(tzMin);
  const tzHH = pad2(Math.floor(absMin / 60));
  const tzMM = pad2(absMin % 60);
  return `${Y}-${M}-${D}T${h}:${m}:${s}${sign}${tzHH}${tzMM}`;
}

// 生成签名参数（不包含 signature），返回包含 signature 的完整参数对象
function buildAuthParams({
  appId,
  accessKeyId,
  accessKeySecret,
  fixedParams,
  uuidStr,
  utcStr,
  extras = {}, // 可选：role_type, pd, eng_punc, eng_vad_mdn 等
}) {
  if (!appId || !accessKeyId || !accessKeySecret) {
    throw new Error("缺少 appId / accessKeyId / accessKeySecret");
  }

  const params = {
    accessKeyId,
    appId,
    uuid: uuidStr || crypto.randomUUID(),
    utc: utcStr || buildUtcString(),
    ...fixedParams,
    ...extras,
  };

  // 升序排序 + URL 编码拼接 baseString（不包含 signature）
  const keys = Object.keys(params).filter((k) => params[k] !== undefined && params[k] !== "");
  keys.sort();
  const baseString = keys
    .map((k) => `${encodeURIComponent(k)}=${encodeURIComponent(String(params[k]))}`)
    .join("&");

  // HMAC-SHA1 + Base64
  const signature = crypto.createHmac("sha1", accessKeySecret).update(baseString).digest("base64");
  return { ...params, signature };
}

// 从 AST 返回消息中提取文字
function extractTextFromResult(msg) {
  // 期望结构见文档：data.cn.st.rt[0].ws[*].cw[0].w
  try {
    const rtArr = msg?.data?.cn?.st?.rt;
    if (!Array.isArray(rtArr) || rtArr.length === 0) return "";

    const wsArr = rtArr[0]?.ws;
    if (!Array.isArray(wsArr)) return "";

    let out = "";
    for (const ws of wsArr) {
      const cw = ws?.cw;
      if (Array.isArray(cw) && cw.length > 0 && typeof cw[0].w === "string") {
        out += cw[0].w;
      }
    }
    return out;
  } catch {
    return "";
  }
}

// sleep
const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

class RTASRClient {
  constructor({
    appId,
    accessKeyId,
    accessKeySecret,
    audioPath,
    baseWsURL = DEFAULT_WS_BASE,
    fixedParams = DEFAULT_FIXED_PARAMS,
    extras = {}, // role_type, pd, eng_punc, eng_vad_mdn...
    debug = process.env.DEBUG === "true",
    pingIntervalSec = 10,
  }) {
    this.appId = appId;
    this.accessKeyId = accessKeyId;
    this.accessKeySecret = accessKeySecret;
    this.audioPath = audioPath;
    this.baseWsURL = baseWsURL;
    this.fixedParams = fixedParams;
    this.extras = extras;
    this.debug = debug;
    this.pingIntervalSec = pingIntervalSec;

    this.ws = null;
    this.isConnected = false;
    this.sessionId = "";
    this.audioFileSize = 0;
    this._pingTimer = null;
  }

  _log(...args) {
    console.log(...args);
  }
  _dbg(...args) {
    if (this.debug) console.log("[DEBUG]", ...args);
  }

  async connect() {
    if (this.isConnected && this.ws) return true;

    // 构造鉴权参数
    const authParams = buildAuthParams({
      appId: this.appId,
      accessKeyId: this.accessKeyId,
      accessKeySecret: this.accessKeySecret,
      fixedParams: this.fixedParams,
      // uuid/utc 默认各自生成
      extras: this.extras,
    });

    // 用 URLSearchParams 生成查询串（会自动 URL 编码）
    const usp = new URLSearchParams(authParams);
    const fullURL = `${this.baseWsURL}?${usp.toString()}`;
    this._dbg("连接URL:", fullURL);

    this.ws = new WebSocket(fullURL, { handshakeTimeout: 15000 });

    return new Promise((resolve) => {
      let resolved = false;

      this.ws.once("open", () => {
        this.isConnected = true;
        this._log("WebSocket 连接成功");
        // 心跳 ping，避免某些边缘网络闲置断开
        this._startPing();
        resolved = true;
        resolve(true);
      });

      this.ws.on("message", (data) => {
        this._onMessage(data);
      });

      this.ws.on("error", (err) => {
        this._log("【错误】WebSocket 异常:", err.message || err);
        if (!resolved) {
          resolved = true;
          resolve(false);
        }
        this.close(); // 主动收尾
      });

      this.ws.once("close", (code, reason) => {
        this._log(`连接关闭 code=${code} reason=${reason?.toString?.() || ""}`);
        this.isConnected = false;
        this._stopPing();
      });

      // 部分服务会先下发会话建立消息，这里不阻塞；仅在 open 成功即返回
    });
  }

  _startPing() {
    this._stopPing();
    if (!this.ws || this.pingIntervalSec <= 0) return;
    this._pingTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        try {
          this.ws.ping();
        } catch { }
      }
    }, this.pingIntervalSec * 1000);
  }

  _stopPing() {
    if (this._pingTimer) {
      clearInterval(this._pingTimer);
      this._pingTimer = null;
    }
  }

  _onMessage(raw) {
    // 文档响应是 JSON
    let msg;
    try {
      // 兼容 Buffer / string
      const text = Buffer.isBuffer(raw) ? raw.toString("utf8") : String(raw);
      msg = JSON.parse(text);
    } catch (e) {
      this._dbg("【调试】收到非 JSON 消息:", e);
      return;
    }

    const msgType = msg?.msg_type; // "action" | "result" | (其他)
    if (msgType === "action") {
      // 取 sessionId
      const sid = msg?.data?.sessionId;
      if (typeof sid === "string" && sid) {
        this.sessionId = sid;
        this._log(`【系统】会话已建立，SessionID: ${sid}`);
      }
      return;
    }

    if (msgType === "result") {
      // 解析识别结果
      const text = extractTextFromResult(msg);
      const type = msg?.data?.cn?.st?.type

      if (text) {


        if (type == '0') {
          // 句子结束
          this._log(`【一句话识别结果】${text}`);
        } else {
          // 中间结果
          this._log(`【中间识别结果】${text}`);
        }
      }

      return;
    }

    // 其他类型（异常/辅助等）
    if (this.debug) {
      this._dbg("【调试】其他类型消息:", msg);
    }
  }

  async sendAudio() {
    if (!this.isConnected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
      this._log("尚未连接，无法发送音频");
      return false;
    }

    // 文件检查
    const audioPathAbs = path.resolve(this.audioPath);
    let stat;
    try {
      stat = fs.statSync(audioPathAbs);
    } catch (e) {
      this._log("打开音频失败:", e.message);
      return false;
    }
    this.audioFileSize = stat.size;

    const totalFrames = Math.ceil(this.audioFileSize / AUDIO_FRAME_SIZE);
    const estimatedDurationSec = (totalFrames * FRAME_INTERVAL_MS) / 1000;
    this._log(
      `音频文件大小: ${this.audioFileSize} 字节, 总帧数: ${totalFrames}, 预估时长: ${estimatedDurationSec.toFixed(
        1
      )} 秒`
    );

    const stream = fs.createReadStream(audioPathAbs, { highWaterMark: AUDIO_FRAME_SIZE });
    let frameIndex = 0;
    const startTs = Date.now();

    for await (const chunk of stream) {
      // 严格按 40ms/帧节流，避免“发送过快引擎出错”
      const expectedTs = startTs + frameIndex * FRAME_INTERVAL_MS;
      const now = Date.now();
      if (expectedTs > now) {
        await sleep(expectedTs - now);
      }

      // 若底层缓冲堆积太多，短暂让出（极端场景兜底）
      while (this.ws.bufferedAmount > AUDIO_FRAME_SIZE * 32) {
        await sleep(5);
      }

      try {
        this.ws.send(chunk, { binary: true });
      } catch (e) {
        this._log("发送音频帧失败:", e.message);
        return false;
      }
      frameIndex++;
    }

    // 结束标记（带上 sessionId 更稳妥）
    const endMsg = { end: true };
    if (this.sessionId) endMsg.sessionId = this.sessionId;

    try {
      this.ws.send(JSON.stringify(endMsg));
    } catch (e) {
      this._log("发送结束标记失败:", e.message);
      return false;
    }

    this._log(`音频发送完成，共发送 ${frameIndex} 帧`);
    return true;
  }

  async waitForResults(extraSeconds = 5) {
    // 根据已知音频长度估算等待时间，+ 若干秒缓冲
    const totalFrames = Math.ceil(this.audioFileSize / AUDIO_FRAME_SIZE);
    const estimatedDuration = (totalFrames * FRAME_INTERVAL_MS) / 1000;
    const waitSec = Math.ceil(estimatedDuration + extraSeconds);
    this._log(`等待结果 ${waitSec} 秒...`);
    await sleep(waitSec * 1000);
  }

  close() {
    try {
      this._stopPing();
      if (this.ws) {
        if (this.ws.readyState === WebSocket.OPEN) {
          this.ws.close();
        }
      }
    } finally {
      this.ws = null;
      this.isConnected = false;
    }
  }
}

// ============ 直接运行（CLI） ============

(async () => {
  const args = parseArgs(process.argv);

  const appId = args.appId || process.env.IFLY_APP_ID || "4d6b4511";
  const accessKeyId = args.akId || process.env.IFLY_AK_ID || "5691d9877efbd1a1785270dedc0d67fe";
  const accessKeySecret = args.akSecret || process.env.IFLY_AK_SECRET || "YjU5ZGE1M2Y2NWFmMTIwYTBjZDc5Njg0";
  const audio = args.audio || process.env.AUDIO || "test.pcm";

  // 可选的业务增强参数（参考文档）
  // role_type=2 开启实时角色分离；pd 指定领域；eng_punc=0 过滤标点；eng_vad_mdn=2 近场
  const extras = {};
  if (args.role_type) extras.role_type = String(args.role_type);
  if (args.pd) extras.pd = String(args.pd);
  if (args.eng_punc) extras.eng_punc = String(args.eng_punc);
  if (args.eng_vad_mdn) extras.eng_vad_mdn = String(args.eng_vad_mdn);

  // 也允许替换 ws 基地址（有灰度/多环境时方便）
  const baseWsURL = args.ws || process.env.IFLY_WS || DEFAULT_WS_BASE;

  // 固定参数可按需改（比如切到 opus，需自行编码，这里仅演示 pcm）
  const fixedParams = {
    audio_encode: args.audio_encode || DEFAULT_FIXED_PARAMS.audio_encode, // "pcm_s16le"
    lang: args.lang || DEFAULT_FIXED_PARAMS.lang, // "autodialect"
    samplerate: args.samplerate || DEFAULT_FIXED_PARAMS.samplerate, // "16000"
  };

  const client = new RTASRClient({
    appId,
    accessKeyId,
    accessKeySecret,
    audioPath: audio,
    baseWsURL,
    fixedParams,
    extras,
    debug: args.debug === "1" || process.env.DEBUG === "true",
  });

  const ok = await client.connect();
  if (!ok) {
    console.error("连接失败");
    process.exit(2);
  }

  const sent = await client.sendAudio();
  if (!sent) {
    console.error("发送音频失败");
    client.close();
    process.exit(3);
  }

  await client.waitForResults(5);
  client.close();
  console.log("程序结束");
})();
